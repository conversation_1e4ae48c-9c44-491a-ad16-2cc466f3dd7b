import requests
import json
import re
import base64
import quopri  # 添加quopri模块用于解码QUOTED-PRINTABLE

# 配置信息
WORKER_DOMAIN = "api.91gmail.cn"  # 后台域名
EMAIL_DOMAIN = "91gmail.cn"  # 域名地址
ADMIN_PASSWORD = "yu6709"  # 管理员密码


def extract_email_body_content(raw_content):
    """专门提取邮件正文内容，而不是邮件头"""
    print("\n📧 正在分析邮件结构...")
    print("=" * 60)
    
    # 首先找到邮件体的开始位置
    # 邮件头和邮件体之间通常有两个连续的换行符
    body_start_patterns = [
        r'\r?\n\r?\n(?=Content-Type:)',  # 找到Content-Type开始的地方
        r'\r?\n\r?\n(?=[^:\r\n])',       # 找到不是头部格式的内容
        r'\r?\n\r?\n'                    # 通用的头体分隔符
    ]
    
    email_body = None
    for pattern in body_start_patterns:
        match = re.search(pattern, raw_content)
        if match:
            email_body = raw_content[match.end():]
            print(f"✅ 找到邮件体，长度: {len(email_body)} 字符")
            break
    
    if not email_body:
        print("❌ 无法分离邮件体，使用完整内容")
        email_body = raw_content
    
    # 显示邮件体的结构信息
    print(f"\n📋 邮件体结构分析:")
    print("-" * 40)
    
    # 查找所有的Content-Type声明
    content_types = re.findall(r'Content-Type:\s*([^\r\n]+)', email_body, re.IGNORECASE)
    if content_types:
        print("发现的内容类型:")
        for i, ct in enumerate(content_types, 1):
            print(f"  {i}. {ct}")
    
    # 查找所有的Content-Transfer-Encoding声明
    encodings = re.findall(r'Content-Transfer-Encoding:\s*([^\r\n]+)', email_body, re.IGNORECASE)
    if encodings:
        print("发现的编码类型:")
        for i, enc in enumerate(encodings, 1):
            print(f"  {i}. {enc}")
    
    print("-" * 40)
    
    decoded_content = None
    content_type = "未知"
    
    # 方法1: 查找并解码QUOTED-PRINTABLE内容
    print("\n🔍 方法1: 查找QUOTED-PRINTABLE编码内容...")
    qp_pattern = r'Content-Transfer-Encoding:\s*quoted-printable.*?\r?\n(?:.*?\r?\n)*?\r?\n(.*?)(?=\r?\n--|\Z)'
    qp_matches = re.findall(qp_pattern, email_body, re.IGNORECASE | re.DOTALL)
    
    if qp_matches:
        print(f"找到 {len(qp_matches)} 个QUOTED-PRINTABLE编码块")
        for i, qp_content in enumerate(qp_matches):
            try:
                # 清理内容
                clean_content = qp_content.strip()
                if len(clean_content) > 20:  # 只处理有意义的内容
                    decoded_part = quopri.decodestring(clean_content.encode()).decode('utf-8', errors='ignore')
                    decoded_part = decoded_part.replace('=\r\n', '').replace('=\n', '')
                    
                    if decoded_content:
                        decoded_content += "\n\n" + decoded_part
                    else:
                        decoded_content = decoded_part
                    content_type = "QUOTED-PRINTABLE"
                    print(f"  ✅ 成功解码块 {i+1}: {len(decoded_part)} 字符")
            except Exception as e:
                print(f"  ❌ 解码块 {i+1} 失败: {e}")
    
    # 方法2: 查找并解码Base64内容
    if not decoded_content:
        print("\n🔍 方法2: 查找Base64编码内容...")
        b64_pattern = r'Content-Transfer-Encoding:\s*base64.*?\r?\n(?:.*?\r?\n)*?\r?\n([A-Za-z0-9+/=\r\n\s]+?)(?=\r?\n--|\Z)'
        b64_matches = re.findall(b64_pattern, email_body, re.IGNORECASE | re.DOTALL)
        
        if b64_matches:
            print(f"找到 {len(b64_matches)} 个Base64编码块")
            for i, b64_content in enumerate(b64_matches):
                try:
                    clean_b64 = re.sub(r'[\r\n\s]', '', b64_content)
                    if len(clean_b64) > 20:  # 只处理有意义的内容
                        decoded_part = base64.b64decode(clean_b64).decode('utf-8', errors='ignore')
                        
                        if decoded_content:
                            decoded_content += "\n\n" + decoded_part
                        else:
                            decoded_content = decoded_part
                        content_type = "Base64"
                        print(f"  ✅ 成功解码块 {i+1}: {len(decoded_part)} 字符")
                except Exception as e:
                    print(f"  ❌ 解码块 {i+1} 失败: {e}")
    
    # 方法3: 查找纯文本内容
    if not decoded_content:
        print("\n🔍 方法3: 查找纯文本内容...")
        # 更精确的纯文本匹配
        text_patterns = [
            r'Content-Type:\s*text/plain.*?\r?\n(?:.*?\r?\n)*?\r?\n(.*?)(?=\r?\n--|\r?\nContent-|\Z)',
            r'Content-Type:\s*text/html.*?\r?\n(?:.*?\r?\n)*?\r?\n(.*?)(?=\r?\n--|\r?\nContent-|\Z)'
        ]
        
        for pattern in text_patterns:
            text_matches = re.findall(pattern, email_body, re.IGNORECASE | re.DOTALL)
            if text_matches:
                print(f"找到 {len(text_matches)} 个文本块")
                for i, text_content in enumerate(text_matches):
                    clean_text = text_content.strip()
                    if len(clean_text) > 20:  # 只处理有意义的内容
                        if decoded_content:
                            decoded_content += "\n\n" + clean_text
                        else:
                            decoded_content = clean_text
                        content_type = "纯文本"
                        print(f"  ✅ 提取文本块 {i+1}: {len(clean_text)} 字符")
                break
    
    # 方法4: 智能提取（最后的尝试）
    if not decoded_content:
        print("\n🔍 方法4: 智能内容提取...")
        # 分割邮件体为段落，过滤掉明显的头部信息
        lines = email_body.split('\n')
        content_lines = []
        in_content = False
        
        for line in lines:
            line = line.strip()
            
            # 跳过空行
            if not line:
                if in_content:
                    content_lines.append('')
                continue
            
            # 跳过明显的邮件头格式
            if ':' in line and len(line.split(':')[0]) < 30 and not in_content:
                continue
            
            # 跳过分隔符
            if line.startswith('--') and len(line) > 10:
                continue
            
            # 如果找到看起来像正文的内容
            if not line.startswith(('Content-', 'MIME-', 'Date:', 'From:', 'To:', 'Subject:')):
                in_content = True
                content_lines.append(line)
        
        if content_lines:
            decoded_content = '\n'.join(content_lines)
            content_type = "智能提取"
            print(f"  ✅ 智能提取到 {len(content_lines)} 行内容")
    
    # 显示提取结果
    if decoded_content and len(decoded_content.strip()) > 0:
        print(f"\n✅ 成功提取邮件正文内容 (方法: {content_type})")
        print("=" * 60)
        print("📄 邮件正文内容:")
        print("-" * 60)
        
        lines = decoded_content.split('\n')
        for i, line in enumerate(lines, 1):
            line = line.strip()
            if len(line) > 0:
                if len(line) > 200:
                    print(f"{i:3d}: {line[:200]}... (行内容过长，已截断)")
                else:
                    print(f"{i:3d}: {line}")
            else:
                print(f"{i:3d}: (空行)")
        
        print("-" * 60)
        print(f"📊 正文统计: 共 {len(lines)} 行，{len(decoded_content)} 字符")
        print("=" * 60)
        
        return decoded_content
    else:
        print("\n❌ 无法提取邮件正文内容")
        print("显示原始邮件体的前50行作为参考:")
        print("-" * 60)
        raw_lines = email_body.split('\n')
        for i, line in enumerate(raw_lines[:50], 1):
            if len(line.strip()) > 0:
                if len(line) > 150:
                    print(f"{i:3d}: {line[:150]}... (原始行，已截断)")
                else:
                    print(f"{i:3d}: {line}")
        if len(raw_lines) > 50:
            print(f"... (还有 {len(raw_lines) - 50} 行未显示)")
        print("-" * 60)
        return email_body


def extract_verification_code(raw_content):
    """从邮件内容中提取验证码"""
    print("\n🔎 开始提取验证码...")

    # 首先提取邮件正文内容
    email_content = extract_email_body_content(raw_content)

    print("\n🔍 正在搜索验证码...")

    # 优先查找用空格分隔的6位数字（最常见的验证码格式）
    spaced_pattern = re.search(r'(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)', email_content)
    if spaced_pattern:
        verification_code = ''.join(spaced_pattern.groups())
        print(f"✅ 找到空格分隔的验证码: {verification_code}")
        return verification_code

    # 查找常见的验证码上下文
    context_patterns = [
        r'(?:code|验证码|verification|one-time)\s*(?:is|为|:)?\s*[:\s]*(\d{6})',
        r'(\d{6})\s*(?:is your|是您的|为您的)?\s*(?:code|验证码|verification)',
        r'Your\s+(?:verification\s+)?code\s*[:\s]*(\d{6})',
        r'验证码\s*[：:\s]*(\d{6})'
    ]

    for pattern in context_patterns:
        match = re.search(pattern, email_content, re.IGNORECASE)
        if match:
            verification_code = match.group(1)
            print(f"✅ 通过上下文找到验证码: {verification_code}")
            return verification_code

    # 如果上述方法都没找到，查找纯文本部分的6位数字
    # 但排除明显不是验证码的数字（如CSS样式中的颜色值等）
    lines = email_content.split('\n')
    for line in lines:
        line = line.strip()
        # 跳过HTML/CSS代码行
        if any(keyword in line.lower() for keyword in ['<', '>', 'style', 'css', 'html', 'color', '#']):
            continue
        # 在纯文本行中查找6位数字
        simple_match = re.search(r'\b(\d{6})\b', line)
        if simple_match:
            verification_code = simple_match.group(1)
            print(f"✅ 在纯文本行找到验证码: {verification_code}")
            return verification_code

    print("❌ 未找到验证码")
    return None


def get_verification_code_by_hidden_email(hidden_email_address):
    """通过隐藏邮箱地址查找验证码"""
    try:
        print(f"\n正在搜索隐藏邮箱 '{hidden_email_address}' 的验证码...")
        
        # 使用管理员权限搜索所有邮件
        res = requests.get(
            f"https://{WORKER_DOMAIN}/admin/mails",
            params={
                "limit": 50,  # 获取更多邮件以提高搜索命中率
                "offset": 0
            },
            headers={
                'x-admin-auth': ADMIN_PASSWORD,
                "Content-Type": "application/json"
            }
        )
        
        if res.status_code == 200:
            data = res.json()
            
            if data.get('results'):
                print(f"正在检查 {len(data['results'])} 封邮件...")
                
                for mail in data['results']:
                    raw_content = mail.get('raw', '')
                    
                    # 查找隐藏邮箱地址
                    hide_email_match = re.search(r'Hide My Email[^<]*<([^>]+@icloud\.com(?:\.cn)?)>', raw_content)
                    if hide_email_match:
                        found_hidden_email = hide_email_match.group(1)
                        
                        # 如果找到匹配的隐藏邮箱地址
                        if found_hidden_email.lower() == hidden_email_address.lower():
                            print(f"\n✅ 找到匹配的隐藏邮箱邮件:")
                            print(f"  发件人: {mail.get('source', '未知')}")
                            print(f"  时间: {mail.get('created_at', '未知')}")
                            print(f"  隐藏邮箱: {found_hidden_email}")
                            
                            # 提取收件人信息
                            to_match = re.search(r'To: (.*?)(?:\r\n|\n)(?![ \t])', raw_content, re.IGNORECASE)
                            if to_match:
                                print(f"  收件人: {to_match.group(1)}")
                            
                            # 提取验证码
                            print(f"\n正在从邮件中提取验证码...")
                            verification_code = extract_verification_code(raw_content)
                            if verification_code:
                                print(f"\n🎉 成功找到验证码: {verification_code}")
                                return verification_code
                            else:
                                print("❌ 在该邮件中未找到验证码")
                                continue
            
            print(f"❌ 未找到隐藏邮箱 '{hidden_email_address}' 的邮件")
            return None
        else:
            print(f"搜索失败: {res.status_code}")
            return None
            
    except Exception as e:
        print(f"搜索出错: {e}")
        return None


def main():
    """主函数 - 通过隐藏邮箱地址获取验证码"""
    print("🔍 Cloudflare Mail 验证码获取工具 (改进版)")
    print("=" * 50)
    
    hidden_email = "<EMAIL>"
    
    if not hidden_email:
        print("❌ 隐藏邮箱地址不能为空")
        return
    
    # 验证输入格式
    if not re.match(r'^[^@]+@icloud\.com(?:\.cn)?$', hidden_email):
        print("❌ 请输入有效的 iCloud 隐藏邮箱地址 (格式: <EMAIL> 或 <EMAIL>)")
        return
    
    print(f"🔎 正在查找隐藏邮箱 '{hidden_email}' 的验证码...")
    verification_code = get_verification_code_by_hidden_email(hidden_email)
    
    if verification_code:
        print(f"\n🎉 成功获取验证码: {verification_code}")
        print("=" * 50)
    else:
        print(f"\n❌ 未能找到隐藏邮箱 '{hidden_email}' 的验证码")
        print("💡 提示: 请确认隐藏邮箱地址正确，且该邮箱最近有收到验证码邮件")
        print("=" * 50)


if __name__ == "__main__":
    main()
