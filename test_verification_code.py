import re

def extract_verification_code_improved(email_content):
    """改进的验证码提取逻辑"""
    print("🔍 正在搜索验证码...")
    
    # 优先查找用空格分隔的6位数字（最常见的验证码格式）
    spaced_pattern = re.search(r'(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)\s+(\d)', email_content)
    if spaced_pattern:
        verification_code = ''.join(spaced_pattern.groups())
        print(f"✅ 找到空格分隔的验证码: {verification_code}")
        return verification_code
    
    # 查找常见的验证码上下文
    context_patterns = [
        r'(?:code|验证码|verification|one-time)\s*(?:is|为|:)?\s*[:\s]*(\d{6})',
        r'(\d{6})\s*(?:is your|是您的|为您的)?\s*(?:code|验证码|verification)',
        r'Your\s+(?:verification\s+)?code\s*[:\s]*(\d{6})',
        r'验证码\s*[：:\s]*(\d{6})'
    ]
    
    for pattern in context_patterns:
        match = re.search(pattern, email_content, re.IGNORECASE)
        if match:
            verification_code = match.group(1)
            print(f"✅ 通过上下文找到验证码: {verification_code}")
            return verification_code
    
    # 如果上述方法都没找到，查找纯文本部分的6位数字
    # 但排除明显不是验证码的数字（如CSS样式中的颜色值等）
    lines = email_content.split('\n')
    for line in lines:
        line = line.strip()
        # 跳过HTML/CSS代码行
        if any(keyword in line.lower() for keyword in ['<', '>', 'style', 'css', 'html', 'color', '#']):
            continue
        # 在纯文本行中查找6位数字
        simple_match = re.search(r'\b(\d{6})\b', line)
        if simple_match:
            verification_code = simple_match.group(1)
            print(f"✅ 在纯文本行找到验证码: {verification_code}")
            return verification_code
    
    print("❌ 未找到验证码")
    return None

# 测试用例1：空格分隔的验证码（Cursor邮件格式）
test_content_1 = """
You requested to sign in to Cursor. Your one-time code is:

8 3 0 2 7 6

<!doctype html><html>
<style>
.button-accent table:active td, .button-accent table:active a {
background-color: #414141 !important;
}
</style>
"""

# 测试用例2：常见的验证码格式
test_content_2 = """
Your verification code is: 123456

Please enter this code to continue.
"""

# 测试用例3：中文验证码格式
test_content_3 = """
您的验证码：789012

请在5分钟内输入此验证码。
"""

print("=" * 50)
print("测试改进的验证码提取逻辑")
print("=" * 50)

print("\n测试用例1 - 空格分隔格式:")
result1 = extract_verification_code_improved(test_content_1)
print(f"结果: {result1}")

print("\n测试用例2 - 标准格式:")
result2 = extract_verification_code_improved(test_content_2)
print(f"结果: {result2}")

print("\n测试用例3 - 中文格式:")
result3 = extract_verification_code_improved(test_content_3)
print(f"结果: {result3}")

print("\n" + "=" * 50)
print("测试完成")
print("=" * 50)
