# Cloudflare Mail 验证码获取工具

## 简化设计说明

这个工具已经按照您的要求进行了简化设计：

### 主要特点

1. **唯一参数**: 只需要输入隐藏邮箱地址（如：`<EMAIL>`）
2. **默认行为**: 自动搜索该隐藏邮箱地址对应的验证码
3. **完整内容显示**: 在提取验证码前，会显示解码后的完整邮件内容

### 使用方法

```bash
python cloudflare_mail.py
```

然后输入隐藏邮箱地址，例如：
```
<EMAIL>
```

### 工作流程

1. **输入验证**: 验证输入的隐藏邮箱地址格式（必须是 `@icloud.com` 或 `@icloud.com.cn`）
2. **邮件搜索**: 在所有邮件中搜索包含该隐藏邮箱地址的邮件
3. **内容解码**: 尝试解码邮件内容（支持 QUOTED-PRINTABLE、Base64、纯文本等格式）
4. **完整显示**: 显示解码后的完整邮件内容，包括行号和统计信息
5. **验证码提取**: 从邮件内容中提取6位数字验证码

### 输出示例

```
🔍 Cloudflare Mail 验证码获取工具
==================================================
请输入隐藏邮箱地址 (例如: <EMAIL>): <EMAIL>

✅ 找到匹配的隐藏邮箱邮件:
  发件人: <EMAIL>
  时间: 2025-07-23 07:36:06
  隐藏邮箱: <EMAIL>
  收件人: Hide My Email <<EMAIL>>

📧 正在解码邮件内容...
============================================================
📄 完整邮件内容:
------------------------------------------------------------
  1: 邮件内容第一行...
  2: 邮件内容第二行...
  ...
------------------------------------------------------------

🔍 正在搜索验证码...
✅ 找到验证码候选: ['175325', ...]
🎯 选择验证码: 175325

🎉 成功获取验证码: 175325
==================================================
```

### 技术特点

- **智能解码**: 支持多种邮件编码格式的自动解码
- **完整显示**: 显示邮件的完整内容，便于调试和验证
- **容错处理**: 即使无法解码也会显示原始内容摘要
- **验证码识别**: 自动识别6位数字验证码

### 配置信息

- **后台域名**: `api.91gmail.cn`
- **邮件域名**: `91gmail.cn`
- **管理员密码**: `yu6709`

所有邮件都发送到 `<EMAIL>`，但通过隐藏邮箱地址来区分不同的验证码请求。
