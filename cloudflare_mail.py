import requests
import json
import re
import base64
import quopri  # 添加quopri模块用于解码QUOTED-PRINTABLE

# 配置信息
WORKER_DOMAIN = "api.91gmail.cn"  # 后台域名
EMAIL_DOMAIN = "91gmail.cn"  # 域名地址
ADMIN_PASSWORD = "yu6709"  # 管理员密码


def extract_and_display_email_content(raw_content):
    """提取并显示完整的邮件内容"""
    print("\n📧 正在解码邮件内容...")
    print("=" * 60)

    decoded_content = None
    content_type = "未知"

    # 方法1: 尝试解析QUOTED-PRINTABLE编码的纯文本
    quoted_printable_match = re.search(r'Content-Transfer-Encoding: QUOTED-PRINTABLE\r\n.*?\r\n\r\n([\s\S]+?)(?:\r\n-+)', raw_content, re.IGNORECASE)
    if quoted_printable_match:
        print("🔍 发现QUOTED-PRINTABLE编码内容，正在解码...")
        quoted_content = quoted_printable_match.group(1)
        try:
            decoded_content = quopri.decodestring(quoted_content).decode('utf-8', errors='ignore')
            # 清理内容，移除编码符号
            decoded_content = decoded_content.replace('=\r\n', '').replace('\r\n', '\n')
            content_type = "QUOTED-PRINTABLE"
        except Exception as e:
            print(f"❌ QUOTED-PRINTABLE解码错误: {e}")

    # 方法2: 尝试解析Base64编码内容
    if not decoded_content:
        base64_match = re.search(r'Content-Transfer-Encoding: base64\r\n\r\n([A-Za-z0-9+/=\r\n]+?)(?:\r\n-+)', raw_content, re.IGNORECASE)
        if base64_match:
            print("🔍 发现Base64编码内容，正在解码...")
            base64_content = base64_match.group(1).replace('\r\n', '').replace('\n', '')
            try:
                decoded_content = base64.b64decode(base64_content).decode('utf-8', errors='ignore')
                content_type = "Base64"
            except Exception as e:
                print(f"❌ Base64解码错误: {e}")

    # 方法3: 查找纯文本部分
    if not decoded_content:
        plain_text_match = re.search(r'Content-Type: text/plain.*?\r\n.*?\r\n\r\n([^-]+?)(?:\r\n-+)', raw_content, re.IGNORECASE)
        if plain_text_match:
            print("� 发现纯文本内容...")
            decoded_content = plain_text_match.group(1)
            content_type = "纯文本"

    # 显示解码后的完整内容
    if decoded_content:
        print(f"\n✅ 成功解码邮件内容 (编码类型: {content_type})")
        print("=" * 60)
        print("📄 完整邮件内容:")
        print("-" * 60)

        lines = decoded_content.split('\n')
        for i, line in enumerate(lines, 1):
            # 显示所有行，但对过长的行进行截断显示
            if len(line.strip()) > 0:
                if len(line) > 200:
                    print(f"{i:3d}: {line[:200]}... (行内容过长，已截断)")
                else:
                    print(f"{i:3d}: {line}")
            else:
                print(f"{i:3d}: (空行)")

        print("-" * 60)
        print(f"📊 邮件统计: 共 {len(lines)} 行")
        print("=" * 60)

        return decoded_content
    else:
        print("❌ 无法解码邮件内容，显示原始内容摘要")
        print("-" * 60)
        # 显示原始内容的前50行作为参考
        raw_lines = raw_content.split('\n')
        for i, line in enumerate(raw_lines[:50], 1):
            if len(line.strip()) > 0:
                if len(line) > 150:
                    print(f"{i:3d}: {line[:150]}... (原始行，已截断)")
                else:
                    print(f"{i:3d}: {line}")
        if len(raw_lines) > 50:
            print(f"... (还有 {len(raw_lines) - 50} 行未显示)")
        print("-" * 60)
        return raw_content


def extract_verification_code(raw_content):
    """从邮件内容中提取验证码"""
    print("\n� 开始提取验证码...")

    # 首先显示完整的邮件内容
    decoded_content = extract_and_display_email_content(raw_content)

    print("\n🔍 正在搜索验证码...")

    # 在解码后的内容中查找6位数字验证码
    verification_matches = re.findall(r'(\d{6})', decoded_content)
    if verification_matches:
        print(f"\n✅ 找到验证码候选: {verification_matches}")
        verification_code = verification_matches[0]  # 取第一个6位数字
        print(f"🎯 选择验证码: {verification_code}")
        return verification_code

    # 如果解码内容中没找到，再尝试原始内容
    if decoded_content != raw_content:
        print("🔄 在解码内容中未找到验证码，尝试搜索原始内容...")
        verification_matches = re.findall(r'(\d{6})', raw_content)
        if verification_matches:
            print(f"\n✅ 在原始内容中找到验证码候选: {verification_matches}")
            verification_code = verification_matches[0]
            print(f"🎯 选择验证码: {verification_code}")
            return verification_code

    print("❌ 未找到6位数字验证码")
    return None


def get_verification_code_by_hidden_email(hidden_email_address):
    """通过隐藏邮箱地址查找验证码"""
    try:
        print(f"\n正在搜索隐藏邮箱 '{hidden_email_address}' 的验证码...")

        # 使用管理员权限搜索所有邮件
        res = requests.get(
            f"https://{WORKER_DOMAIN}/admin/mails",
            params={
                "limit": 50,  # 获取更多邮件以提高搜索命中率
                "offset": 0
            },
            headers={
                'x-admin-auth': ADMIN_PASSWORD,
                "Content-Type": "application/json"
            }
        )

        if res.status_code == 200:
            data = res.json()

            if data.get('results'):
                print(f"正在检查 {len(data['results'])} 封邮件...")

                for mail in data['results']:
                    raw_content = mail.get('raw', '')

                    # 查找隐藏邮箱地址
                    hide_email_match = re.search(r'Hide My Email[^<]*<([^>]+@icloud\.com(?:\.cn)?)>', raw_content)
                    if hide_email_match:
                        found_hidden_email = hide_email_match.group(1)

                        # 如果找到匹配的隐藏邮箱地址
                        if found_hidden_email.lower() == hidden_email_address.lower():
                            print(f"\n✅ 找到匹配的隐藏邮箱邮件:")
                            print(f"  发件人: {mail.get('source', '未知')}")
                            print(f"  时间: {mail.get('created_at', '未知')}")
                            print(f"  隐藏邮箱: {found_hidden_email}")

                            # 提取收件人信息
                            to_match = re.search(r'To: (.*?)(?:\r\n|\n)(?![ \t])', raw_content, re.IGNORECASE)
                            if to_match:
                                print(f"  收件人: {to_match.group(1)}")

                            # 提取验证码
                            print(f"\n正在从邮件中提取验证码...")
                            verification_code = extract_verification_code(raw_content)
                            if verification_code:
                                print(f"\n🎉 成功找到验证码: {verification_code}")
                                return verification_code
                            else:
                                print("❌ 在该邮件中未找到验证码")
                                continue

            print(f"❌ 未找到隐藏邮箱 '{hidden_email_address}' 的邮件")
            return None
        else:
            print(f"搜索失败: {res.status_code}")
            return None

    except Exception as e:
        print(f"搜索出错: {e}")
        return None


def main():
    """主函数 - 通过隐藏邮箱地址获取验证码"""
    hidden_email = "<EMAIL>"

    if not hidden_email:
        print("❌ 隐藏邮箱地址不能为空")
        return

    # 验证输入格式
    if not re.match(r'^[^@]+@icloud\.com(?:\.cn)?$', hidden_email):
        print("❌ 请输入有效的 iCloud 隐藏邮箱地址 (格式: <EMAIL> 或 <EMAIL>)")
        return

    print(f"🔎 正在查找隐藏邮箱 '{hidden_email}' 的验证码...")
    verification_code = get_verification_code_by_hidden_email(hidden_email)

    if verification_code:
        print(f"\n🎉 成功获取验证码: {verification_code}")
        print("=" * 50)
    else:
        print(f"\n❌ 未能找到隐藏邮箱 '{hidden_email}' 的验证码")
        print("💡 提示: 请确认隐藏邮箱地址正确，且该邮箱最近有收到验证码邮件")
        print("=" * 50)


if __name__ == "__main__":
    main()